import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useApp } from './context/AppContext';
import MainLayout from './components/MainLayout';
import DataEntry from './components/DataEntry';
import StatementEligibles from './components/StatementEligibles';
import AdvancedSearch from './components/AdvancedSearch';
import Settings from './components/Settings';

function App() {
  const [activeTab, setActiveTab] = useState('data-entry');
  const { theme } = useApp();

  const renderActiveComponent = () => {
    switch (activeTab) {
      case 'data-entry':
        return <DataEntry />;
      case 'eligibles':
        return <StatementEligibles />;
      case 'search':
        return <AdvancedSearch />;
      case 'settings':
        return <Settings />;
      default:
        return <DataEntry />;
    }
  };

  return (
    <div 
      className="min-h-screen"
      data-theme={theme}
      style={{
        background: 'var(--bg-primary)',
        color: 'var(--text-primary)'
      }}
    >
      <MainLayout activeTab={activeTab} setActiveTab={setActiveTab}>
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full"
          >
            {renderActiveComponent()}
          </motion.div>
        </AnimatePresence>
      </MainLayout>
    </div>
  );
}

export default App;
