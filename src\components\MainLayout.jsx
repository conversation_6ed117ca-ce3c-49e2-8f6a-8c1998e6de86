import React from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Users, 
  Search, 
  Settings,
  Database,
  User
} from 'lucide-react';

const MainLayout = ({ children, activeTab, setActiveTab }) => {
  const tabs = [
    {
      id: 'data-entry',
      label: 'إدخال البيانات',
      icon: FileText,
      color: 'from-blue-500 to-purple-600'
    },
    {
      id: 'eligibles',
      label: 'المستحقون للكشوفات',
      icon: Users,
      color: 'from-green-500 to-teal-600'
    },
    {
      id: 'search',
      label: 'البحث المتقدم',
      icon: Search,
      color: 'from-orange-500 to-red-600'
    },
    {
      id: 'settings',
      label: 'الإعدادات',
      icon: Settings,
      color: 'from-purple-500 to-pink-600'
    }
  ];

  return (
    <div className="flex flex-col h-screen">
      {/* Header */}
      <header className="glass p-4 m-4 mb-0 rounded-t-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse">
              <Database className="w-8 h-8 text-blue-400" />
              <h1 className="text-2xl font-bold gradient-text">
                برنامج استحقاق كشف عمل
              </h1>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="flex items-center space-x-2 space-x-reverse">
              <User className="w-5 h-5" />
              <span className="text-sm font-medium gradient-text">
                المبرمج: علي عاجل خشان المحنّة
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="glass mx-4 p-2">
        <div className="flex space-x-2 space-x-reverse">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <motion.button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  relative flex items-center space-x-2 space-x-reverse px-6 py-3 rounded-xl
                  font-medium transition-all duration-300 btn-hover
                  ${isActive 
                    ? 'text-white shadow-lg' 
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }
                `}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className={`absolute inset-0 bg-gradient-to-r ${tab.color} rounded-xl`}
                    initial={false}
                    transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                  />
                )}
                <div className="relative flex items-center space-x-2 space-x-reverse">
                  <Icon className="w-5 h-5" />
                  <span>{tab.label}</span>
                </div>
              </motion.button>
            );
          })}
        </div>
      </nav>

      {/* Main Content */}
      <main className="flex-1 glass m-4 mt-2 rounded-b-2xl p-6 overflow-hidden">
        {children}
      </main>
    </div>
  );
};

export default MainLayout;
