import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Save, User, Building, Calendar, CheckCircle, AlertCircle } from 'lucide-react';
import { useApp } from '../context/AppContext';

const DataEntry = () => {
  const { addEmployee, arabicMonths } = useApp();
  const [formData, setFormData] = useState({
    name: '',
    department: '',
    first_month: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim() || !formData.department.trim() || !formData.first_month) {
      setMessage({
        type: 'error',
        text: 'يرجى ملء جميع الحقول المطلوبة'
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      const result = await addEmployee(formData);
      
      if (result.success) {
        setMessage({
          type: 'success',
          text: 'تم حفظ بيانات الموظف بنجاح'
        });
        
        // تفريغ الحقول
        setFormData({
          name: '',
          department: '',
          first_month: ''
        });
      } else {
        setMessage({
          type: 'error',
          text: result.error || 'حدث خطأ أثناء حفظ البيانات'
        });
      }
    } catch (error) {
      setMessage({
        type: 'error',
        text: 'حدث خطأ غير متوقع'
      });
    } finally {
      setIsSubmitting(false);
      
      // إخفاء الرسالة بعد 3 ثوان
      setTimeout(() => {
        setMessage({ type: '', text: '' });
      }, 3000);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Header */}
      <div className="text-center mb-8">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-center space-x-3 space-x-reverse mb-4"
        >
          <User className="w-8 h-8 text-blue-400" />
          <h2 className="text-3xl font-bold gradient-text">إدخال بيانات الموظفين</h2>
        </motion.div>
        <p className="text-gray-300">أدخل بيانات الموظف الجديد لإضافته إلى النظام</p>
      </div>

      {/* Form */}
      <motion.form
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        onSubmit={handleSubmit}
        className="glass p-8 rounded-2xl space-y-6"
      >
        {/* اسم الموظف */}
        <div className="space-y-2">
          <label className="flex items-center space-x-2 space-x-reverse text-lg font-medium">
            <User className="w-5 h-5 text-blue-400" />
            <span>اسم الموظف</span>
            <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            placeholder="أدخل اسم الموظف الكامل"
            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl
                     focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent
                     placeholder-gray-400 text-white transition-all duration-300"
            required
          />
        </div>

        {/* القسم */}
        <div className="space-y-2">
          <label className="flex items-center space-x-2 space-x-reverse text-lg font-medium">
            <Building className="w-5 h-5 text-green-400" />
            <span>القسم</span>
            <span className="text-red-400">*</span>
          </label>
          <input
            type="text"
            name="department"
            value={formData.department}
            onChange={handleInputChange}
            placeholder="أدخل اسم القسم"
            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl
                     focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent
                     placeholder-gray-400 text-white transition-all duration-300"
            required
          />
        </div>

        {/* أول شهر للكشف */}
        <div className="space-y-2">
          <label className="flex items-center space-x-2 space-x-reverse text-lg font-medium">
            <Calendar className="w-5 h-5 text-purple-400" />
            <span>أول شهر للكشف</span>
            <span className="text-red-400">*</span>
          </label>
          <select
            name="first_month"
            value={formData.first_month}
            onChange={handleInputChange}
            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl
                     focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent
                     text-white transition-all duration-300"
            required
          >
            <option value="" className="bg-gray-800">اختر الشهر</option>
            {arabicMonths.map((month, index) => (
              <option key={index} value={month} className="bg-gray-800">
                {month}
              </option>
            ))}
          </select>
        </div>

        {/* زر الحفظ */}
        <motion.button
          type="submit"
          disabled={isSubmitting}
          className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white
                   py-4 px-6 rounded-xl font-bold text-lg btn-hover
                   disabled:opacity-50 disabled:cursor-not-allowed
                   flex items-center justify-center space-x-2 space-x-reverse"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {isSubmitting ? (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
          ) : (
            <>
              <Save className="w-6 h-6" />
              <span>حفظ البيانات</span>
            </>
          )}
        </motion.button>

        {/* رسالة النتيجة */}
        {message.text && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`flex items-center space-x-2 space-x-reverse p-4 rounded-xl ${
              message.type === 'success' 
                ? 'bg-green-500/20 border border-green-500/30 text-green-300'
                : 'bg-red-500/20 border border-red-500/30 text-red-300'
            }`}
          >
            {message.type === 'success' ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <AlertCircle className="w-5 h-5" />
            )}
            <span>{message.text}</span>
          </motion.div>
        )}
      </motion.form>
    </div>
  );
};

export default DataEntry;
