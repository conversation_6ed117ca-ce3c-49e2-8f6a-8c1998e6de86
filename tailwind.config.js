/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'turquoise-blue': '#00D4FF',
        'neon-purple': '#8B5CF6',
        'electric-pink': '#FF0080',
        'lime-green': '#32FF32',
        'volcanic': '#FF4500',
        'icy': '#E0F6FF',
        'sky': '#87CEEB',
        'purple-blue': '#6A5ACD',
        'blue-sky': '#4169E1',
        'dark': '#1a1a1a'
      },
      fontFamily: {
        'arabic': ['Tajawal', 'Arial', 'sans-serif'],
      },
      animation: {
        'gradient': 'gradient 15s ease infinite',
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      keyframes: {
        gradient: {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          }
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' }
        }
      }
    },
  },
  plugins: [],
}
