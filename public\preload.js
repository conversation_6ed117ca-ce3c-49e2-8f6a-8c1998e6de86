const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  // قاعدة البيانات
  addEmployee: (employee) => ipcRenderer.invoke('db-add-employee', employee),
  getEmployees: () => ipcRenderer.invoke('db-get-employees'),
  updateEmployee: (id, employee) => ipcRenderer.invoke('db-update-employee', id, employee),
  deleteEmployee: (id) => ipcRenderer.invoke('db-delete-employee', id),
  
  // تصدير Excel
  exportToExcel: (data, filename) => ipcRenderer.invoke('export-to-excel', data, filename)
});
