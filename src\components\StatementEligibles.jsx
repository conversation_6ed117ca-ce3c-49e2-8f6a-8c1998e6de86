import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Users, 
  Edit, 
  Trash2, 
  Download,
  CheckCircle,
  Clock,
  AlertTriangle,
  User,
  Building,
  Calendar,
  Save,
  X
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const StatementEligibles = () => {
  const { 
    employees, 
    updateEmployee, 
    deleteEmployee, 
    exportToExcel,
    arabicMonths,
    getMonthNumber 
  } = useApp();
  
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [editForm, setEditForm] = useState({
    name: '',
    department: '',
    first_month: ''
  });
  const [isExporting, setIsExporting] = useState(false);

  // تحويل الشهر العربي إلى ميلادي
  const convertToGregorian = (arabicMonth) => {
    const monthNumber = getMonthNumber(arabicMonth);
    return monthNumber ? monthNumber.toString().padStart(2, '0') : '';
  };

  // الحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'تم التسليم':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'سيستحق كشف بعد شهر':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'مطلوب كشف':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  // الحصول على أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'تم التسليم':
        return <CheckCircle className="w-4 h-4" />;
      case 'سيستحق كشف بعد شهر':
        return <Clock className="w-4 h-4" />;
      case 'مطلوب كشف':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  // بدء التعديل
  const startEdit = (employee) => {
    setEditingEmployee(employee.id);
    setEditForm({
      name: employee.name,
      department: employee.department,
      first_month: employee.first_month
    });
  };

  // إلغاء التعديل
  const cancelEdit = () => {
    setEditingEmployee(null);
    setEditForm({
      name: '',
      department: '',
      first_month: ''
    });
  };

  // حفظ التعديل
  const saveEdit = async () => {
    try {
      const result = await updateEmployee(editingEmployee, editForm);
      if (result.success) {
        setEditingEmployee(null);
        setEditForm({
          name: '',
          department: '',
          first_month: ''
        });
      } else {
        alert('حدث خطأ أثناء التحديث: ' + result.error);
      }
    } catch (error) {
      alert('حدث خطأ غير متوقع');
    }
  };

  // حذف موظف
  const handleDelete = async (id, name) => {
    if (window.confirm(`هل أنت متأكد من حذف الموظف "${name}"؟`)) {
      try {
        const result = await deleteEmployee(id);
        if (!result.success) {
          alert('حدث خطأ أثناء الحذف: ' + result.error);
        }
      } catch (error) {
        alert('حدث خطأ غير متوقع');
      }
    }
  };

  // تصدير إلى Excel
  const handleExport = async () => {
    setIsExporting(true);
    try {
      const exportData = employees.map(emp => ({
        'الاسم': emp.name,
        'القسم': emp.department,
        'أول شهر للكشف (ميلادي)': convertToGregorian(emp.first_month),
        'الحالة': emp.status,
        'تاريخ الإضافة': new Date(emp.created_at).toLocaleDateString('ar-SA')
      }));

      const result = await exportToExcel(exportData, `الموظفين_${new Date().toISOString().split('T')[0]}`);
      
      if (result.success) {
        alert('تم تصدير الملف بنجاح إلى مجلد التحميلات');
      } else {
        alert('حدث خطأ أثناء التصدير: ' + result.error);
      }
    } catch (error) {
      alert('حدث خطأ غير متوقع');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex items-center space-x-3 space-x-reverse"
        >
          <Users className="w-8 h-8 text-green-400" />
          <h2 className="text-3xl font-bold gradient-text">المستحقون للكشوفات</h2>
        </motion.div>

        <motion.button
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          onClick={handleExport}
          disabled={isExporting || employees.length === 0}
          className="bg-gradient-to-r from-green-500 to-teal-600 text-white
                   px-6 py-3 rounded-xl font-medium btn-hover
                   disabled:opacity-50 disabled:cursor-not-allowed
                   flex items-center space-x-2 space-x-reverse"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {isExporting ? (
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
          ) : (
            <>
              <Download className="w-5 h-5" />
              <span>تصدير Excel</span>
            </>
          )}
        </motion.button>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="glass p-4 rounded-xl"
        >
          <div className="flex items-center space-x-3 space-x-reverse">
            <CheckCircle className="w-8 h-8 text-green-400" />
            <div>
              <p className="text-2xl font-bold text-green-400">
                {employees.filter(emp => emp.status === 'تم التسليم').length}
              </p>
              <p className="text-sm text-gray-300">تم التسليم</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="glass p-4 rounded-xl"
        >
          <div className="flex items-center space-x-3 space-x-reverse">
            <Clock className="w-8 h-8 text-yellow-400" />
            <div>
              <p className="text-2xl font-bold text-yellow-400">
                {employees.filter(emp => emp.status === 'سيستحق كشف بعد شهر').length}
              </p>
              <p className="text-sm text-gray-300">سيستحق بعد شهر</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="glass p-4 rounded-xl"
        >
          <div className="flex items-center space-x-3 space-x-reverse">
            <AlertTriangle className="w-8 h-8 text-red-400" />
            <div>
              <p className="text-2xl font-bold text-red-400">
                {employees.filter(emp => emp.status === 'مطلوب كشف').length}
              </p>
              <p className="text-sm text-gray-300">مطلوب كشف</p>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Employees Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="glass rounded-xl overflow-hidden"
      >
        {employees.length === 0 ? (
          <div className="p-12 text-center">
            <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-400 mb-2">لا توجد بيانات موظفين</h3>
            <p className="text-gray-500">قم بإضافة موظفين جدد من تبويب "إدخال البيانات"</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الاسم</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">القسم</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">أول شهر (ميلادي)</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الحالة</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الإجراءات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                <AnimatePresence>
                  {employees.map((employee, index) => (
                    <motion.tr
                      key={employee.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ delay: index * 0.1 }}
                      className="hover:bg-white/5 transition-colors duration-200"
                    >
                      {editingEmployee === employee.id ? (
                        // Edit Mode
                        <>
                          <td className="px-6 py-4">
                            <input
                              type="text"
                              value={editForm.name}
                              onChange={(e) => setEditForm({...editForm, name: e.target.value})}
                              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                                       focus:outline-none focus:ring-2 focus:ring-blue-400 text-white"
                            />
                          </td>
                          <td className="px-6 py-4">
                            <input
                              type="text"
                              value={editForm.department}
                              onChange={(e) => setEditForm({...editForm, department: e.target.value})}
                              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                                       focus:outline-none focus:ring-2 focus:ring-blue-400 text-white"
                            />
                          </td>
                          <td className="px-6 py-4">
                            <select
                              value={editForm.first_month}
                              onChange={(e) => setEditForm({...editForm, first_month: e.target.value})}
                              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                                       focus:outline-none focus:ring-2 focus:ring-blue-400 text-white"
                            >
                              {arabicMonths.map((month, idx) => (
                                <option key={idx} value={month} className="bg-gray-800">
                                  {month}
                                </option>
                              ))}
                            </select>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm border ${getStatusColor(employee.status)}`}>
                              {getStatusIcon(employee.status)}
                              <span>{employee.status}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button
                                onClick={saveEdit}
                                className="p-2 bg-green-500/20 text-green-400 rounded-lg hover:bg-green-500/30 transition-colors"
                              >
                                <Save className="w-4 h-4" />
                              </button>
                              <button
                                onClick={cancelEdit}
                                className="p-2 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </>
                      ) : (
                        // View Mode
                        <>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-3 space-x-reverse">
                              <User className="w-5 h-5 text-blue-400" />
                              <span className="font-medium">{employee.name}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Building className="w-4 h-4 text-green-400" />
                              <span>{employee.department}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <Calendar className="w-4 h-4 text-purple-400" />
                              <span>{convertToGregorian(employee.first_month)}</span>
                            </div>
                          </td>
                          <td className="px-6 py-4">
                            <span className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm border ${getStatusColor(employee.status)}`}>
                              {getStatusIcon(employee.status)}
                              <span>{employee.status}</span>
                            </span>
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <button
                                onClick={() => startEdit(employee)}
                                className="p-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDelete(employee.id, employee.name)}
                                className="p-2 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </>
                      )}
                    </motion.tr>
                  ))}
                </AnimatePresence>
              </tbody>
            </table>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default StatementEligibles;
