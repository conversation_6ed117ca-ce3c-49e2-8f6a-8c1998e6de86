# برنامج استحقاق كشف عمل - Work Statement Eligibility Program

<div align="center">

![Logo](https://via.placeholder.com/200x100/667eea/ffffff?text=برنامج+استحقاق+كشف+عمل)

**نظام ذكي لإدارة ومتابعة كشوفات العمل للموظفين**

**المبرمج: علي عاجل خشان المحنّة**

[![Made with ❤️ in Iraq](https://img.shields.io/badge/Made%20with%20❤️%20in-Iraq-green.svg)](https://github.com/aliajil8)
[![React](https://img.shields.io/badge/React-18.2.0-blue.svg)](https://reactjs.org/)
[![Electron](https://img.shields.io/badge/Electron-27.1.3-purple.svg)](https://electronjs.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

</div>

## 📋 نظرة عامة

برنامج استحقاق كشف عمل هو تطبيق سطح مكتب مفتوح المصدر مصمم لإدارة ومتابعة كشوفات العمل للموظفين بطريقة ذكية وآلية. يوفر البرنامج واجهة عربية حديثة وجذابة مع إمكانيات متقدمة لإدارة البيانات.

## ✨ المميزات الرئيسية

### 🎯 الوظائف الأساسية
- **إدخال البيانات**: إضافة وتعديل بيانات الموظفين بسهولة
- **حساب الاستحقاق**: حساب استحقاق الكشوفات تلقائياً بعد 3 أشهر
- **البحث المتقدم**: بحث وتصفية متقدمة بمعايير متعددة
- **تصدير البيانات**: تصدير البيانات بصيغة XLS
- **الحفظ الدائم**: حفظ البيانات محلياً مع إمكانية النسخ الاحتياطي

### 🎨 التصميم والواجهة
- **واجهة عربية**: تصميم متجاوب يدعم اللغة العربية بالكامل
- **تأثيرات بصرية**: تأثيرات Glassmorphism وحركات سلسة
- **ألوان متدرجة**: نظام ألوان حديث مع تدرجات جذابة
- **أنماط متعددة**: 7 أنماط مختلفة (البركاني، الثلجي، السمائي، إلخ)
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات

### 🔧 التقنيات المستخدمة
- **Frontend**: React.js 18+ مع Hooks
- **Styling**: Tailwind CSS + Custom CSS
- **Animations**: Framer Motion
- **Desktop**: Electron للتطبيق المكتبي
- **Storage**: JSON للحفظ المحلي
- **Icons**: Lucide React
- **Build Tool**: Vite

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- Node.js 18 أو أحدث
- npm أو yarn
- نظام التشغيل: Windows, macOS, أو Linux

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/aliajil8/work-statement-desktop.git
cd work-statement-desktop
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **تشغيل التطبيق في وضع التطوير**
```bash
npm run electron-dev
```

4. **بناء التطبيق للإنتاج**
```bash
npm run dist
```

## 📁 هيكل المشروع

```
work-statement-desktop/
├── public/
│   ├── electron.js          # العملية الرئيسية لـ Electron
│   ├── preload.js          # سكريبت التحميل المسبق
│   └── index.html          # ملف HTML الرئيسي
├── src/
│   ├── components/         # مكونات React
│   │   ├── MainLayout.jsx
│   │   ├── DataEntry.jsx
│   │   ├── StatementEligibles.jsx
│   │   ├── AdvancedSearch.jsx
│   │   └── Settings.jsx
│   ├── context/           # إدارة الحالة
│   │   └── AppContext.jsx
│   ├── App.jsx            # المكون الرئيسي
│   ├── index.css          # الأنماط المخصصة
│   └── main.jsx           # نقطة الدخول
├── package.json           # تبعيات المشروع
└── README.md             # هذا الملف
```

## 🎮 كيفية الاستخدام

### 1. إدخال البيانات
- انتقل إلى تبويب "إدخال البيانات"
- أدخل اسم الموظف والقسم (إدخال يدوي) وأول شهر للكشف (قائمة منسدلة بالأشهر العربية)
- اضغط "حفظ البيانات"

### 2. عرض المستحقين
- انتقل إلى تبويب "المستحقين للكشوفات"
- شاهد قائمة الموظفين مع حالة الاستحقاق
- استخدم أزرار التعديل والحذف حسب الحاجة
- اضغط زر "تصدير Excel" لتصدير البيانات بصيغة XLS

### 3. البحث المتقدم
- انتقل إلى تبويب "البحث المتقدم"
- استخدم الفلاتر للبحث بالاسم أو القسم أو الشهر أو الحالة
- صدّر النتائج بصيغة XLS

### 4. الإعدادات
- انتقل إلى تبويب "الإعدادات"
- غيّر المظهر (7 أنماط مختلفة)
- شاهد معلومات المبرمج

## 📊 حالات الاستحقاق

- **تم التسليم**: الموظف أكمل 3 أشهر أو أكثر
- **سيستحق كشف بعد شهر**: الموظف أكمل شهرين ويحتاج شهر واحد
- **مطلوب كشف**: الموظف لم يكمل الشهرين بعد

## 🔒 الأمان والخصوصية

- **حفظ محلي**: جميع البيانات محفوظة محلياً على جهاز المستخدم
- **لا توجد خوادم خارجية**: لا يتم إرسال البيانات لأي خادم خارجي
- **نسخ احتياطية**: إمكانية عمل نسخ احتياطية منتظمة

## 👨‍💻 المطور

**علي عاجل خشان المحنّة**
- 📱 موبايل + واتساب: 07727232639
- 📧 البريد الإلكتروني: <EMAIL>
- 🌍 الموقع: العراق

### الخبرات البرمجية
- JavaScript & TypeScript
- React.js & Next.js
- Node.js & Express
- Python & Django
- PHP & Laravel
- Java & Spring Boot
- C# & .NET
- Flutter & Dart
- React Native
- Vue.js & Nuxt.js
- Angular
- MongoDB & MySQL
- PostgreSQL & SQLite
- Docker & Kubernetes
- AWS & Azure
- Git & GitHub

## 📝 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

---

<div align="center">

**صُنع بـ ❤️ في العراق**

بواسطة المبرمج: علي عاجل خشان المحنّة

موبايل + واتساب: 07727232639 | ايميل: <EMAIL>

</div>
