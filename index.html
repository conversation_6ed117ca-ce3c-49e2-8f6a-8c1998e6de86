<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>برنامج استحقاق كشف عمل</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Tajawal', Arial, sans-serif;
        margin: 0;
        padding: 0;
        overflow: hidden;
      }
      
      .splash-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        animation: fadeOut 3s ease-in-out 2s forwards;
      }
      
      .logo-container {
        text-align: center;
        animation: slideUp 1s ease-out;
      }
      
      .main-title {
        font-size: 3rem;
        font-weight: 900;
        background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
        background-size: 300% 300%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradient 3s ease infinite;
        margin-bottom: 1rem;
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
      }
      
      .programmer-name {
        font-size: 1.5rem;
        font-weight: 700;
        background: linear-gradient(45deg, #8B5CF6, #FF0080, #FF4500, #00D4FF);
        background-size: 300% 300%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradient 2s ease infinite;
        margin-bottom: 2rem;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid #fff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes fadeOut {
        to {
          opacity: 0;
          visibility: hidden;
        }
      }
      
      @keyframes slideUp {
        from {
          transform: translateY(50px);
          opacity: 0;
        }
        to {
          transform: translateY(0);
          opacity: 1;
        }
      }
      
      @keyframes gradient {
        0%, 100% {
          background-position: 0% 50%;
        }
        50% {
          background-position: 100% 50%;
        }
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div class="splash-screen" id="splash">
      <div class="logo-container">
        <h1 class="main-title">برنامج استحقاق كشف عمل</h1>
        <p class="programmer-name">المبرمج: علي عاجل خشان المحنّة</p>
        <div class="loading-spinner"></div>
      </div>
    </div>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
    <script>
      setTimeout(() => {
        const splash = document.getElementById('splash');
        if (splash) {
          splash.style.display = 'none';
        }
      }, 4000);
    </script>
  </body>
</html>
