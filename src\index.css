@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Taja<PERSON>', Arial, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #8B5CF6, #00D4FF);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #7C3AED, #0EA5E9);
}

/* Glassmorphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(45deg, #8B5CF6, #FF0080, #FF4500, #00D4FF);
  background-size: 300% 300%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

/* Button animations */
.btn-hover {
  transition: all 0.3s ease;
  transform: translateY(0);
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Theme variables */
:root {
  --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-secondary: rgba(255, 255, 255, 0.1);
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --accent: #8B5CF6;
}

[data-theme="volcanic"] {
  --bg-primary: linear-gradient(135deg, #ff4500 0%, #ff6347 100%);
  --accent: #ff4500;
}

[data-theme="icy"] {
  --bg-primary: linear-gradient(135deg, #e0f6ff 0%, #b3e5fc 100%);
  --text-primary: #1a1a1a;
  --text-secondary: #4a5568;
  --accent: #0ea5e9;
}

[data-theme="sky"] {
  --bg-primary: linear-gradient(135deg, #87ceeb 0%, #4169e1 100%);
  --accent: #4169e1;
}

[data-theme="purple-blue"] {
  --bg-primary: linear-gradient(135deg, #6a5acd 0%, #4169e1 100%);
  --accent: #6a5acd;
}

[data-theme="blue-sky"] {
  --bg-primary: linear-gradient(135deg, #4169e1 0%, #87ceeb 100%);
  --accent: #4169e1;
}

[data-theme="dark"] {
  --bg-primary: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  --bg-secondary: rgba(255, 255, 255, 0.05);
  --text-primary: #ffffff;
  --text-secondary: #a0a0a0;
  --accent: #8B5CF6;
}
