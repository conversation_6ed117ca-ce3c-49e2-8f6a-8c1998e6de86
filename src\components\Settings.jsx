import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings as SettingsIcon, 
  Palette, 
  User, 
  Code,
  Phone,
  Mail,
  MapPin,
  Heart,
  Smartphone,
  MessageCircle
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const Settings = () => {
  const { theme, setTheme } = useApp();

  const themes = [
    {
      id: 'default',
      name: 'الافتراضي',
      colors: ['#667eea', '#764ba2'],
      description: 'التصميم الافتراضي الأنيق'
    },
    {
      id: 'volcanic',
      name: 'البركاني',
      colors: ['#ff4500', '#ff6347'],
      description: 'تصميم ناري وقوي'
    },
    {
      id: 'icy',
      name: 'الثلجي',
      colors: ['#e0f6ff', '#b3e5fc'],
      description: 'تصميم بارد ومنعش'
    },
    {
      id: 'sky',
      name: 'السمائي',
      colors: ['#87ceeb', '#4169e1'],
      description: 'تصميم سمائي هادئ'
    },
    {
      id: 'purple-blue',
      name: 'البنفسجي والأزرق',
      colors: ['#6a5acd', '#4169e1'],
      description: 'تصميم ملكي أنيق'
    },
    {
      id: 'blue-sky',
      name: 'الأزرق والسمائي',
      colors: ['#4169e1', '#87ceeb'],
      description: 'تصميم أزرق متدرج'
    },
    {
      id: 'dark',
      name: 'الداكن',
      colors: ['#1a1a1a', '#2d2d2d'],
      description: 'تصميم داكن للعيون'
    }
  ];

  const programmingSkills = [
    'JavaScript & TypeScript',
    'React.js & Next.js',
    'Node.js & Express',
    'Python & Django',
    'PHP & Laravel',
    'Java & Spring Boot',
    'C# & .NET',
    'Flutter & Dart',
    'React Native',
    'Vue.js & Nuxt.js',
    'Angular',
    'MongoDB & MySQL',
    'PostgreSQL & SQLite',
    'Docker & Kubernetes',
    'AWS & Azure',
    'Git & GitHub'
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center space-x-3 space-x-reverse"
      >
        <SettingsIcon className="w-8 h-8 text-purple-400" />
        <h2 className="text-3xl font-bold gradient-text">الإعدادات</h2>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Theme Settings */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.1 }}
          className="glass p-6 rounded-xl"
        >
          <div className="flex items-center space-x-3 space-x-reverse mb-6">
            <Palette className="w-6 h-6 text-purple-400" />
            <h3 className="text-xl font-bold">أنماط التصميم</h3>
          </div>

          <div className="grid grid-cols-1 gap-3">
            {themes.map((themeOption) => (
              <motion.button
                key={themeOption.id}
                onClick={() => setTheme(themeOption.id)}
                className={`
                  p-4 rounded-xl border-2 transition-all duration-300 text-right
                  ${theme === themeOption.id 
                    ? 'border-purple-400 bg-purple-500/20' 
                    : 'border-white/20 bg-white/5 hover:bg-white/10'
                  }
                `}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="flex space-x-1 space-x-reverse">
                      {themeOption.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 rounded-full border-2 border-white/30"
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                    <div>
                      <h4 className="font-medium">{themeOption.name}</h4>
                      <p className="text-sm text-gray-400">{themeOption.description}</p>
                    </div>
                  </div>
                  {theme === themeOption.id && (
                    <div className="w-4 h-4 bg-purple-400 rounded-full"></div>
                  )}
                </div>
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Developer Info */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="glass p-6 rounded-xl"
        >
          <div className="flex items-center space-x-3 space-x-reverse mb-6">
            <User className="w-6 h-6 text-blue-400" />
            <h3 className="text-xl font-bold">نبذة عن المبرمج</h3>
          </div>

          <div className="space-y-6">
            {/* Developer Name */}
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full 
                            flex items-center justify-center mx-auto mb-4">
                <User className="w-10 h-10 text-white" />
              </div>
              <h4 className="text-2xl font-bold gradient-text mb-2">علي عاجل خشان المحنّة</h4>
              <p className="text-gray-400">مطور برمجيات محترف</p>
            </div>

            {/* Skills */}
            <div>
              <div className="flex items-center space-x-2 space-x-reverse mb-3">
                <Code className="w-5 h-5 text-green-400" />
                <h5 className="font-medium">الخبرات البرمجية</h5>
              </div>
              <div className="grid grid-cols-2 gap-2">
                {programmingSkills.slice(0, 8).map((skill, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.3 + index * 0.1 }}
                    className="bg-white/10 px-3 py-2 rounded-lg text-sm text-center"
                  >
                    {skill}
                  </motion.div>
                ))}
              </div>
              <p className="text-xs text-gray-400 mt-2 text-center">
                وأكثر من 10 تقنيات أخرى...
              </p>
            </div>

            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="flex items-center space-x-2 space-x-reverse">
                  <Smartphone className="w-5 h-5 text-green-400" />
                  <MessageCircle className="w-5 h-5 text-green-400" />
                </div>
                <div>
                  <p className="text-sm text-gray-400">موبايل + واتساب</p>
                  <p className="font-medium text-green-400">07727232639</p>
                </div>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <Mail className="w-5 h-5 text-blue-400" />
                <div>
                  <p className="text-sm text-gray-400">البريد الإلكتروني</p>
                  <p className="font-medium text-blue-400"><EMAIL></p>
                </div>
              </div>

              <div className="flex items-center space-x-3 space-x-reverse">
                <MapPin className="w-5 h-5 text-red-400" />
                <div>
                  <p className="text-sm text-gray-400">الموقع</p>
                  <p className="font-medium text-red-400">العراق</p>
                </div>
              </div>
            </div>

            {/* Made with Love */}
            <div className="text-center pt-4 border-t border-white/20">
              <div className="flex items-center justify-center space-x-2 space-x-reverse">
                <span className="text-gray-400">صُنع بـ</span>
                <Heart className="w-5 h-5 text-red-400 animate-pulse" />
                <span className="text-gray-400">في العراق</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Settings;
