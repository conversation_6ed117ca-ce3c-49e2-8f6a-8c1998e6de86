import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { 
  Search, 
  Filter, 
  Download,
  User,
  Building,
  Calendar,
  CheckCircle,
  Clock,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { useApp } from '../context/AppContext';

const AdvancedSearch = () => {
  const { employees, exportToExcel, arabicMonths, getMonthNumber } = useApp();
  const [filters, setFilters] = useState({
    name: '',
    department: '',
    fromMonth: '',
    toMonth: '',
    status: ''
  });
  const [isExporting, setIsExporting] = useState(false);

  // تحويل الشهر العربي إلى ميلادي
  const convertToGregorian = (arabicMonth) => {
    const monthNumber = getMonthNumber(arabicMonth);
    return monthNumber ? monthNumber.toString().padStart(2, '0') : '';
  };

  // تصفية الموظفين
  const filteredEmployees = useMemo(() => {
    return employees.filter(employee => {
      const nameMatch = !filters.name || 
        employee.name.toLowerCase().includes(filters.name.toLowerCase());
      
      const departmentMatch = !filters.department || 
        employee.department.toLowerCase().includes(filters.department.toLowerCase());
      
      const statusMatch = !filters.status || employee.status === filters.status;
      
      // تصفية الأشهر
      let monthMatch = true;
      if (filters.fromMonth || filters.toMonth) {
        const empMonthNum = getMonthNumber(employee.first_month);
        const fromMonthNum = filters.fromMonth ? getMonthNumber(filters.fromMonth) : 1;
        const toMonthNum = filters.toMonth ? getMonthNumber(filters.toMonth) : 12;
        
        if (empMonthNum) {
          monthMatch = empMonthNum >= fromMonthNum && empMonthNum <= toMonthNum;
        }
      }
      
      return nameMatch && departmentMatch && statusMatch && monthMatch;
    });
  }, [employees, filters, getMonthNumber]);

  // تحديث الفلاتر
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // مسح الفلاتر
  const clearFilters = () => {
    setFilters({
      name: '',
      department: '',
      fromMonth: '',
      toMonth: '',
      status: ''
    });
  };

  // تصدير النتائج
  const handleExport = async () => {
    setIsExporting(true);
    try {
      const exportData = filteredEmployees.map(emp => ({
        'الاسم': emp.name,
        'القسم': emp.department,
        'أول شهر للكشف (ميلادي)': convertToGregorian(emp.first_month),
        'الحالة': emp.status,
        'تاريخ الإضافة': new Date(emp.created_at).toLocaleDateString('ar-SA')
      }));

      const result = await exportToExcel(exportData, `نتائج_البحث_${new Date().toISOString().split('T')[0]}`);
      
      if (result.success) {
        alert('تم تصدير نتائج البحث بنجاح إلى مجلد التحميلات');
      } else {
        alert('حدث خطأ أثناء التصدير: ' + result.error);
      }
    } catch (error) {
      alert('حدث خطأ غير متوقع');
    } finally {
      setIsExporting(false);
    }
  };

  // الحصول على لون الحالة
  const getStatusColor = (status) => {
    switch (status) {
      case 'تم التسليم':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'سيستحق كشف بعد شهر':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'مطلوب كشف':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  // الحصول على أيقونة الحالة
  const getStatusIcon = (status) => {
    switch (status) {
      case 'تم التسليم':
        return <CheckCircle className="w-4 h-4" />;
      case 'سيستحق كشف بعد شهر':
        return <Clock className="w-4 h-4" />;
      case 'مطلوب كشف':
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="flex items-center space-x-3 space-x-reverse"
        >
          <Search className="w-8 h-8 text-orange-400" />
          <h2 className="text-3xl font-bold gradient-text">البحث المتقدم</h2>
        </motion.div>

        <div className="flex items-center space-x-3 space-x-reverse">
          <motion.button
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            onClick={clearFilters}
            className="bg-gradient-to-r from-gray-500 to-gray-600 text-white
                     px-4 py-2 rounded-xl font-medium btn-hover
                     flex items-center space-x-2 space-x-reverse"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <RefreshCw className="w-4 h-4" />
            <span>مسح الفلاتر</span>
          </motion.button>

          <motion.button
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            onClick={handleExport}
            disabled={isExporting || filteredEmployees.length === 0}
            className="bg-gradient-to-r from-orange-500 to-red-600 text-white
                     px-6 py-3 rounded-xl font-medium btn-hover
                     disabled:opacity-50 disabled:cursor-not-allowed
                     flex items-center space-x-2 space-x-reverse"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {isExporting ? (
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            ) : (
              <>
                <Download className="w-5 h-5" />
                <span>تصدير النتائج</span>
              </>
            )}
          </motion.button>
        </div>
      </div>

      {/* Search Filters */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="glass p-6 rounded-xl"
      >
        <div className="flex items-center space-x-2 space-x-reverse mb-6">
          <Filter className="w-6 h-6 text-blue-400" />
          <h3 className="text-xl font-bold">فلاتر البحث</h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          {/* البحث بالاسم */}
          <div className="space-y-2">
            <label className="flex items-center space-x-2 space-x-reverse text-sm font-medium">
              <User className="w-4 h-4 text-blue-400" />
              <span>الاسم</span>
            </label>
            <input
              type="text"
              value={filters.name}
              onChange={(e) => handleFilterChange('name', e.target.value)}
              placeholder="ابحث بالاسم..."
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                       focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent
                       placeholder-gray-400 text-white text-sm"
            />
          </div>

          {/* البحث بالقسم */}
          <div className="space-y-2">
            <label className="flex items-center space-x-2 space-x-reverse text-sm font-medium">
              <Building className="w-4 h-4 text-green-400" />
              <span>القسم</span>
            </label>
            <input
              type="text"
              value={filters.department}
              onChange={(e) => handleFilterChange('department', e.target.value)}
              placeholder="ابحث بالقسم..."
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                       focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent
                       placeholder-gray-400 text-white text-sm"
            />
          </div>

          {/* من شهر */}
          <div className="space-y-2">
            <label className="flex items-center space-x-2 space-x-reverse text-sm font-medium">
              <Calendar className="w-4 h-4 text-purple-400" />
              <span>من شهر</span>
            </label>
            <select
              value={filters.fromMonth}
              onChange={(e) => handleFilterChange('fromMonth', e.target.value)}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                       focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent
                       text-white text-sm"
            >
              <option value="" className="bg-gray-800">جميع الأشهر</option>
              {arabicMonths.map((month, index) => (
                <option key={index} value={month} className="bg-gray-800">
                  {month}
                </option>
              ))}
            </select>
          </div>

          {/* إلى شهر */}
          <div className="space-y-2">
            <label className="flex items-center space-x-2 space-x-reverse text-sm font-medium">
              <Calendar className="w-4 h-4 text-purple-400" />
              <span>إلى شهر</span>
            </label>
            <select
              value={filters.toMonth}
              onChange={(e) => handleFilterChange('toMonth', e.target.value)}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                       focus:outline-none focus:ring-2 focus:ring-purple-400 focus:border-transparent
                       text-white text-sm"
            >
              <option value="" className="bg-gray-800">جميع الأشهر</option>
              {arabicMonths.map((month, index) => (
                <option key={index} value={month} className="bg-gray-800">
                  {month}
                </option>
              ))}
            </select>
          </div>

          {/* الحالة */}
          <div className="space-y-2">
            <label className="flex items-center space-x-2 space-x-reverse text-sm font-medium">
              <CheckCircle className="w-4 h-4 text-yellow-400" />
              <span>الحالة</span>
            </label>
            <select
              value={filters.status}
              onChange={(e) => handleFilterChange('status', e.target.value)}
              className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg
                       focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent
                       text-white text-sm"
            >
              <option value="" className="bg-gray-800">جميع الحالات</option>
              <option value="تم التسليم" className="bg-gray-800">تم التسليم</option>
              <option value="سيستحق كشف بعد شهر" className="bg-gray-800">سيستحق كشف بعد شهر</option>
              <option value="مطلوب كشف" className="bg-gray-800">مطلوب كشف</option>
            </select>
          </div>
        </div>
      </motion.div>

      {/* Results Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
        className="glass p-4 rounded-xl"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 space-x-reverse">
            <Search className="w-5 h-5 text-blue-400" />
            <span className="font-medium">نتائج البحث:</span>
            <span className="text-blue-400 font-bold">{filteredEmployees.length}</span>
            <span>من أصل</span>
            <span className="text-gray-400">{employees.length}</span>
            <span>موظف</span>
          </div>
        </div>
      </motion.div>

      {/* Results Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="glass rounded-xl overflow-hidden"
      >
        {filteredEmployees.length === 0 ? (
          <div className="p-12 text-center">
            <Search className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-400 mb-2">لا توجد نتائج</h3>
            <p className="text-gray-500">جرب تعديل معايير البحث للحصول على نتائج</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-white/5">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الاسم</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">القسم</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">أول شهر (ميلادي)</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">الحالة</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-gray-300">تاريخ الإضافة</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-white/10">
                {filteredEmployees.map((employee, index) => (
                  <motion.tr
                    key={employee.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="hover:bg-white/5 transition-colors duration-200"
                  >
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <User className="w-5 h-5 text-blue-400" />
                        <span className="font-medium">{employee.name}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Building className="w-4 h-4 text-green-400" />
                        <span>{employee.department}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <Calendar className="w-4 h-4 text-purple-400" />
                        <span>{convertToGregorian(employee.first_month)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <span className={`inline-flex items-center space-x-1 space-x-reverse px-3 py-1 rounded-full text-sm border ${getStatusColor(employee.status)}`}>
                        {getStatusIcon(employee.status)}
                        <span>{employee.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 text-gray-400 text-sm">
                      {new Date(employee.created_at).toLocaleDateString('ar-SA')}
                    </td>
                  </motion.tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </motion.div>
    </div>
  );
};

export default AdvancedSearch;
