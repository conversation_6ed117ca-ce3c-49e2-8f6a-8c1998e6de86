{"name": "work-statement-desktop", "version": "1.0.0", "description": "برنامج استحقاق كشف عمل - Work Statement Eligibility Program", "main": "public/electron.js", "homepage": "./", "private": true, "author": "علي عاجل خشان المحنّة", "license": "MIT", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "concurrently \"npm run dev\" \"wait-on http://localhost:5173 && electron .\"", "electron-build": "npm run build && electron-builder", "dist": "npm run build && electron-builder --publish=never"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "framer-motion": "^10.16.4", "lucide-react": "^0.294.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.6.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "vite": "^4.5.0", "wait-on": "^7.2.0"}, "build": {"appId": "com.aliajil.workstatement", "productName": "برنامج استحقاق كشف عمل", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "public/preload.js", "node_modules/**/*"], "mac": {"category": "public.app-category.business"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}