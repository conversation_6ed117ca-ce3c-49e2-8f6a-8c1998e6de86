import React, { createContext, useContext, useState, useEffect } from 'react';

const AppContext = createContext();

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export const AppProvider = ({ children }) => {
  const [employees, setEmployees] = useState([]);
  const [theme, setTheme] = useState('default');
  const [loading, setLoading] = useState(false);

  // الأشهر العربية
  const arabicMonths = [
    'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
    'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
  ];

  // تحويل الشهر العربي إلى رقم
  const getMonthNumber = (arabicMonth) => {
    return arabicMonths.indexOf(arabicMonth) + 1;
  };

  // تحويل رقم الشهر إلى اسم عربي
  const getArabicMonth = (monthNumber) => {
    return arabicMonths[monthNumber - 1] || '';
  };

  // حساب حالة الاستحقاق
  const calculateEligibilityStatus = (firstMonth) => {
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;
    const currentYear = currentDate.getFullYear();
    
    const firstMonthNumber = getMonthNumber(firstMonth);
    if (!firstMonthNumber) return 'مطلوب كشف';
    
    // حساب الفرق بالأشهر
    let monthsDiff = (currentYear * 12 + currentMonth) - (currentYear * 12 + firstMonthNumber);
    
    if (monthsDiff >= 3) {
      return 'تم التسليم';
    } else if (monthsDiff === 2) {
      return 'سيستحق كشف بعد شهر';
    } else {
      return 'مطلوب كشف';
    }
  };

  // جلب الموظفين من قاعدة البيانات
  const fetchEmployees = async () => {
    setLoading(true);
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.getEmployees();
        if (result.success) {
          const employeesWithStatus = result.data.map(emp => ({
            ...emp,
            status: calculateEligibilityStatus(emp.first_month)
          }));
          setEmployees(employeesWithStatus);
        }
      }
    } catch (error) {
      console.error('Error fetching employees:', error);
    } finally {
      setLoading(false);
    }
  };

  // إضافة موظف جديد
  const addEmployee = async (employeeData) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.addEmployee(employeeData);
        if (result.success) {
          await fetchEmployees();
          return { success: true };
        }
        return { success: false, error: result.error };
      }
      return { success: false, error: 'Electron API not available' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // تحديث موظف
  const updateEmployee = async (id, employeeData) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.updateEmployee(id, employeeData);
        if (result.success) {
          await fetchEmployees();
          return { success: true };
        }
        return { success: false, error: result.error };
      }
      return { success: false, error: 'Electron API not available' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // حذف موظف
  const deleteEmployee = async (id) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.deleteEmployee(id);
        if (result.success) {
          await fetchEmployees();
          return { success: true };
        }
        return { success: false, error: result.error };
      }
      return { success: false, error: 'Electron API not available' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  // تصدير إلى Excel
  const exportToExcel = async (data, filename) => {
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.exportToExcel(data, filename);
        return result;
      }
      return { success: false, error: 'Electron API not available' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  useEffect(() => {
    fetchEmployees();
  }, []);

  const value = {
    employees,
    setEmployees,
    theme,
    setTheme,
    loading,
    setLoading,
    arabicMonths,
    getMonthNumber,
    getArabicMonth,
    calculateEligibilityStatus,
    fetchEmployees,
    addEmployee,
    updateEmployee,
    deleteEmployee,
    exportToExcel
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};
