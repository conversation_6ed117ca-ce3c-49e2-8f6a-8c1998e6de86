const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const XLSX = require('xlsx');
const fs = require('fs');

// استخدام تخزين محلي مبسط
let employees = [];

let mainWindow;
let db;

// إنشاء قاعدة البيانات المحلية
function createDatabase() {
  const dataPath = path.join(app.getPath('userData'), 'employees.json');

  try {
    if (fs.existsSync(dataPath)) {
      const data = fs.readFileSync(dataPath, 'utf8');
      employees = JSON.parse(data);
    }
  } catch (error) {
    console.error('Error loading data:', error);
    employees = [];
  }

  console.log('Local storage initialized');
}

// حفظ البيانات
function saveData() {
  const dataPath = path.join(app.getPath('userData'), 'employees.json');
  try {
    fs.writeFileSync(dataPath, JSON.stringify(employees, null, 2));
  } catch (error) {
    console.error('Error saving data:', error);
  }
}

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'icon.png'),
    show: false,
    titleBarStyle: 'default'
  });

  const startUrl = isDev 
    ? 'http://localhost:5173' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  if (isDev) {
    mainWindow.webContents.openDevTools();
  }
}

app.whenReady().then(() => {
  createDatabase();
  createWindow();
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    saveData();
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers لقاعدة البيانات
ipcMain.handle('db-add-employee', async (event, employee) => {
  try {
    const newEmployee = {
      id: Date.now(),
      name: employee.name,
      department: employee.department,
      first_month: employee.first_month,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    employees.push(newEmployee);
    saveData();

    return { success: true, id: newEmployee.id };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-get-employees', async () => {
  try {
    return { success: true, data: employees.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)) };
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-update-employee', async (event, id, employee) => {
  try {
    const index = employees.findIndex(emp => emp.id === id);
    if (index !== -1) {
      employees[index] = {
        ...employees[index],
        name: employee.name,
        department: employee.department,
        first_month: employee.first_month,
        updated_at: new Date().toISOString()
      };
      saveData();
      return { success: true };
    } else {
      return { success: false, error: 'Employee not found' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('db-delete-employee', async (event, id) => {
  try {
    const index = employees.findIndex(emp => emp.id === id);
    if (index !== -1) {
      employees.splice(index, 1);
      saveData();
      return { success: true };
    } else {
      return { success: false, error: 'Employee not found' };
    }
  } catch (error) {
    return { success: false, error: error.message };
  }
});

ipcMain.handle('export-to-excel', async (event, data, filename) => {
  try {
    const ws = XLSX.utils.json_to_sheet(data);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, 'الموظفين');
    
    const filePath = path.join(app.getPath('downloads'), `${filename}.xls`);
    XLSX.writeFile(wb, filePath);
    
    return { success: true, path: filePath };
  } catch (error) {
    return { success: false, error: error.message };
  }
});
